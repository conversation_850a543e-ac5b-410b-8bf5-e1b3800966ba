{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/models/Admin.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst AdminSchema = new mongoose.Schema({\n  username: {\n    type: String,\n    required: [true, 'Username is required'],\n    trim: true,\n    unique: true,\n    maxlength: [50, 'Username cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    trim: true,\n    lowercase: true,\n    unique: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  phone: {\n    type: String,\n    required: [true, 'Phone number is required'],\n    trim: true,\n    maxlength: [20, 'Phone number cannot be more than 20 characters']\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters']\n  },\n  admin: {\n    type: Boolean,\n    default: true,\n    required: true\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  },\n  updatedAt: {\n    type: Date,\n    default: Date.now\n  },\n  lastLogin: {\n    type: Date\n  }\n});\n\n// Hash password before saving\nAdminSchema.pre('save', async function(next) {\n  // Only hash the password if it has been modified (or is new)\n  if (!this.isModified('password')) return next();\n  \n  try {\n    // Hash password with cost of 12\n    const hashedPassword = await bcrypt.hash(this.password, 12);\n    this.password = hashedPassword;\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Update the updatedAt field before saving\nAdminSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\n// Instance method to check password\nAdminSchema.methods.comparePassword = async function(candidatePassword) {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Create indexes for better query performance\nAdminSchema.index({ email: 1 });\nAdminSchema.index({ username: 1 });\n\nexport default mongoose.models.Admin || mongoose.model('Admin', AdminSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,cAAc,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACtC,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;YAAI;SAA6C;IAC/D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,MAAM;QACN,WAAW;QACX,QAAQ;QACR,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;QACN,WAAW;YAAC;YAAI;SAAiD;IACnE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;IAC1D;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,WAAW;QACT,MAAM;IACR;AACF;AAEA,8BAA8B;AAC9B,YAAY,GAAG,CAAC,QAAQ,eAAe,IAAI;IACzC,6DAA6D;IAC7D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,gCAAgC;QAChC,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACxD,IAAI,CAAC,QAAQ,GAAG;QAChB;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,2CAA2C;AAC3C,YAAY,GAAG,CAAC,QAAQ,SAAS,IAAI;IACnC,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;IACzB;AACF;AAEA,oCAAoC;AACpC,YAAY,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACpE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,8CAA8C;AAC9C,YAAY,KAAK,CAAC;IAAE,OAAO;AAAE;AAC7B,YAAY,KAAK,CAAC;IAAE,UAAU;AAAE;uCAEjB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,SAAS", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/app/api/admin/auth/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Admin from '@/models/Admin';\n\n// POST - Admin Login\nexport async function POST(request) {\n  try {\n    // Connect to database\n    await connectDB();\n\n    // Parse request body\n    const body = await request.json();\n    const { email, password } = body;\n\n    // Validate required fields\n    if (!email || !password) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Email and password are required' \n        },\n        { status: 400 }\n      );\n    }\n\n    // Find admin by email\n    const admin = await Admin.findOne({ email: email.toLowerCase().trim() });\n    \n    if (!admin) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Invalid email or password' \n        },\n        { status: 401 }\n      );\n    }\n\n    // Check password\n    const isPasswordValid = await admin.comparePassword(password);\n    \n    if (!isPasswordValid) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Invalid email or password' \n        },\n        { status: 401 }\n      );\n    }\n\n    // Update last login\n    admin.lastLogin = new Date();\n    await admin.save();\n\n    // Return success response (excluding password)\n    return NextResponse.json(\n      {\n        success: true,\n        message: 'Login successful',\n        admin: {\n          id: admin._id,\n          username: admin.username,\n          email: admin.email,\n          phone: admin.phone,\n          admin: admin.admin,\n          lastLogin: admin.lastLogin\n        }\n      },\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Error during admin login:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Internal server error' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// GET - Check if admin exists (for setup)\nexport async function GET() {\n  try {\n    await connectDB();\n    \n    const adminCount = await Admin.countDocuments();\n    \n    return NextResponse.json({\n      success: true,\n      adminExists: adminCount > 0,\n      count: adminCount\n    });\n    \n  } catch (error) {\n    console.error('Error checking admin:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Internal server error' \n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,sBAAsB;QACtB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,qBAAqB;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;QAE5B,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,WAAW,GAAG,IAAI;QAAG;QAEtE,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,kBAAkB,MAAM,MAAM,eAAe,CAAC;QAEpD,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,SAAS,GAAG,IAAI;QACtB,MAAM,MAAM,IAAI;QAEhB,+CAA+C;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,OAAO;gBACL,IAAI,MAAM,GAAG;gBACb,UAAU,MAAM,QAAQ;gBACxB,OAAO,MAAM,KAAK;gBAClB,OAAO,MAAM,KAAK;gBAClB,OAAO,MAAM,KAAK;gBAClB,WAAW,MAAM,SAAS;YAC5B;QACF,GACA;YAAE,QAAQ;QAAI;IAGlB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,aAAa,MAAM,wHAAA,CAAA,UAAK,CAAC,cAAc;QAE7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,aAAa,aAAa;YAC1B,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}