{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/components/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\n\r\nexport default function Header() {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n\r\n  const scrollToSection = (sectionId: string) => {\r\n    const element = document.getElementById(sectionId);\r\n    if (element) {\r\n      element.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n    setIsMenuOpen(false);\r\n  };\r\n\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm shadow-sm z-50\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between items-center py-4\">\r\n          {/* Logo */}\r\n          <div className=\"flex items-center\">\r\n            <h1 className=\"text-2xl font-bold text-blue-900\">\r\n              Residential Rehab\r\n            </h1>\r\n          </div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <nav className=\"hidden md:flex space-x-8\">\r\n            <button\r\n              onClick={() => scrollToSection('home')}\r\n              className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200\"\r\n            >\r\n              Home\r\n            </button>\r\n            <button\r\n              onClick={() => scrollToSection('about')}\r\n              className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200\"\r\n            >\r\n              About Us\r\n            </button>\r\n            <button\r\n              onClick={() => scrollToSection('services')}\r\n              className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200\"\r\n            >\r\n              What We Do\r\n            </button>\r\n            <button\r\n              onClick={() => scrollToSection('faq')}\r\n              className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200\"\r\n            >\r\n              FAQ\r\n            </button>\r\n            <button\r\n              onClick={() => scrollToSection('contact')}\r\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200\"\r\n            >\r\n              Contact Us\r\n            </button>\r\n          </nav>\r\n\r\n          {/* Mobile menu button */}\r\n          <button\r\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n            className=\"md:hidden text-gray-700\"\r\n          >\r\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Mobile Navigation */}\r\n        {isMenuOpen && (\r\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\r\n            <div className=\"flex flex-col space-y-4\">\r\n              <button\r\n                onClick={() => scrollToSection('home')}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200 text-left\"\r\n              >\r\n                Home\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection('about')}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200 text-left\"\r\n              >\r\n                About Us\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection('services')}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200 text-left\"\r\n              >\r\n                What We Do\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection('faq')}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200 text-left\"\r\n              >\r\n                FAQ\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection('contact')}\r\n                className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center\"\r\n              >\r\n                Contact Us\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;sCAMnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAM1E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA3GwB;KAAA", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/components/Hero.tsx"], "sourcesContent": ["'use client';\r\n\r\nexport default function Hero() {\r\n  const scrollToContact = () => {\r\n    const element = document.getElementById('contact');\r\n    if (element) {\r\n      element.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100 pt-20\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n        <div className=\"animate-fade-in-up\">\r\n          <h1 className=\"text-4xl md:text-6xl font-bold text-blue-900 mb-6 leading-tight text-center\">\r\n            Residential Rehab Inc\r\n          </h1>\r\n          \r\n          <p className=\"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\">\r\n            We understand that your home is more than just a property it's where life happens. Since 1996, we've been proud to support homeowners across South Florida with trusted guidance, care-driven property solutions, and long-term stability.\r\n          </p>\r\n          \r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n            <button\r\n              onClick={scrollToContact}\r\n              className=\"bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg\"\r\n            >\r\n              Get Your Offer Now\r\n            </button>\r\n            \r\n            <button\r\n              onClick={() => {\r\n                const element = document.getElementById('about');\r\n                if (element) {\r\n                  element.scrollIntoView({ behavior: 'smooth' });\r\n                }\r\n              }}\r\n              className=\"border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-200\"\r\n            >\r\n              Learn More\r\n            </button>\r\n          </div>\r\n          \r\n          <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\r\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\r\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">30</div>\r\n              <div className=\"text-gray-700\">Years of Experience</div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\r\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">200+</div>\r\n              <div className=\"text-gray-700\">Home owners Helped</div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\r\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">100%</div>\r\n              <div className=\"text-gray-700\">Owner Satisfaction</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,MAAM,kBAAkB;QACtB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8E;;;;;;kCAI5F,6LAAC;wBAAE,WAAU;kCAA2E;;;;;;kCAIxF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS;oCACP,MAAM,UAAU,SAAS,cAAc,CAAC;oCACxC,IAAI,SAAS;wCACX,QAAQ,cAAc,CAAC;4CAAE,UAAU;wCAAS;oCAC9C;gCACF;gCACA,WAAU;0CACX;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAGjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAGjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;KA7DwB", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/components/About.tsx"], "sourcesContent": ["'use client';\r\n\r\nexport default function About() {\r\n  return (\r\n    <section id=\"about\" className=\"py-20 bg-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl font-bold text-blue-900 mb-4\">About Us</h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n            Building trust through decades of successful real estate investments\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\r\n          <div className=\"space-y-6\">\r\n            <h3 className=\"text-3xl font-bold text-blue-900\">\r\n              Our Story Since 1996\r\n            </h3>\r\n            \r\n            <p className=\"text-lg text-gray-700 leading-relaxed\">\r\nFor nearly three decades, Presidential Real Estate Holdings has proudly served the homeowners of South Florida with care, integrity, and dedication. Since 1996, we've helped hundreds of families transition through life&apos;s changes offering solutions that are fast, fair, and respectful of what home truly means.\r\n\r\nRooted in the heart of South Florida&apos;s east coast, our deep local knowledge and reliable track record have made us a trusted partner for those looking to sell with peace of mind — and for those looking to invest in the future of our thriving communities.\r\n            </p>\r\n            \r\n            <p className=\"text-lg text-gray-700 leading-relaxed\">\r\n              Our deep understanding of the local market, combined with our proven track \r\n              record, makes us the trusted choice for property owners looking to sell \r\n              quickly and investors seeking premium opportunities.\r\n            </p>\r\n\r\n            <div className=\"bg-blue-50 p-6 rounded-lg\">\r\n              <h4 className=\"text-xl font-semibold text-blue-900 mb-3\">Why Choose Us?</h4>\r\n              <ul className=\"space-y-2 text-gray-700\">\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  Decades of proven experience in South Florida\r\n                </li>\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  Hundreds of successful property acquisitions\r\n                </li>\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  Fast, reliable, and transparent transactions\r\n                </li>\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  Deep local market knowledge and connections\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-6\">\r\n            <div className=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white p-8 rounded-lg\">\r\n              <h4 className=\"text-2xl font-bold mb-4\">Our Track Record</h4>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span>Years in Business</span>\r\n                  <span className=\"text-2xl font-bold\">30</span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span>Home Owners Helped</span>\r\n                  <span className=\"text-2xl font-bold\">200+</span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span>Market Focus</span>\r\n                  <span className=\"text-lg font-semibold\">South FL East Coast</span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span>Founded</span>\r\n                  <span className=\"text-2xl font-bold\">1996</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-gray-50 p-6 rounded-lg\">\r\n              <h4 className=\"text-xl font-semibold text-blue-900 mb-3\">Our Mission</h4>\r\n              <p className=\"text-gray-700\">\r\nTo support homeowners with fast, fair, and trustworthy real estate solutions whether you're navigating change, seeking peace of mind, or ready for a fresh start. At the same time, we are committed to strengthening South Florida communities by investing in quality properties that foster long-term growth and value.\r\n\r\n\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAIjD,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAMrD,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAMrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;;;;;;;;;;;;;8CAK3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3C;KAhGwB", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/components/Services.tsx"], "sourcesContent": ["'use client';\r\n\r\nexport default function Services() {\r\n  const service = {\r\n    title: \"Property Acquisitions\",\r\n    description: \"We purchase residential and commercial properties directly from owners, offering fast closings and competitive prices.\",\r\n    icon: (\r\n      <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\r\n      </svg>\r\n    ),\r\n    features: [\"Quick cash offers\", \"No repairs needed\", \"Fast 7-14 day closings\", \"No realtor fees\"]\r\n  };\r\n\r\n  return (\r\n    <section id=\"services\" className=\"py-20 bg-gray-50\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl font-bold text-blue-900 mb-4\">What We Do</h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n          we buy houses for cash fast           </p>\r\n        </div>\r\n\r\n        {/* Main Service Section */}\r\n        <div className=\"max-w-4xl mx-auto mb-16\">\r\n          <div className=\"bg-white p-12 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300\">\r\n            <div className=\"text-center mb-8\">\r\n              <div className=\"text-blue-600 mb-6 flex justify-center\">\r\n                {service.icon}\r\n              </div>\r\n              <h3 className=\"text-3xl font-bold text-blue-900 mb-6\">{service.title}</h3>\r\n              <p className=\"text-gray-700 text-lg leading-relaxed max-w-2xl mx-auto\">{service.description}</p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-8\">\r\n              {service.features.map((feature, featureIndex) => (\r\n                <div key={featureIndex} className=\"flex items-center text-gray-700 bg-blue-50 p-4 rounded-lg\">\r\n                  <svg className=\"w-5 h-5 text-blue-600 mr-4 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  <span className=\"font-medium\">{feature}</span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-blue-600 text-white p-8 rounded-lg text-center\">\r\n          <h3 className=\"text-3xl font-bold mb-4\">Ready to Sell Your Property?</h3>\r\n          <p className=\"text-xl mb-6 opacity-90\">\r\n            Get a fair cash offer for your property today. No repairs, no fees, no hassle.\r\n            We make selling your home fast and stress-free.\r\n          </p>\r\n          <button\r\n            onClick={() => {\r\n              const element = document.getElementById('contact');\r\n              if (element) {\r\n                element.scrollIntoView({ behavior: 'smooth' });\r\n              }\r\n            }}\r\n            className=\"bg-white text-blue-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200\"\r\n          >\r\n            Get Your Cash Offer Now\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,MAAM,UAAU;QACd,OAAO;QACP,aAAa;QACb,oBACE,6LAAC;YAAI,WAAU;YAAY,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACnE,cAAA,6LAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,UAAU;YAAC;YAAqB;YAAqB;YAA0B;SAAkB;IACnG;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCAAG,WAAU;kDAAyC,QAAQ,KAAK;;;;;;kDACpE,6LAAC;wCAAE,WAAU;kDAA2D,QAAQ,WAAW;;;;;;;;;;;;0CAG7F,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wCAAuB,WAAU;;0DAChC,6LAAC;gDAAI,WAAU;gDAA2C,MAAK;gDAAe,SAAQ;0DACpF,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;0DAE3J,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;uCAJvB;;;;;;;;;;;;;;;;;;;;;8BAWlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAIvC,6LAAC;4BACC,SAAS;gCACP,MAAM,UAAU,SAAS,cAAc,CAAC;gCACxC,IAAI,SAAS;oCACX,QAAQ,cAAc,CAAC;wCAAE,UAAU;oCAAS;gCAC9C;4BACF;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAlEwB", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/components/FAQ.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function FAQ() {\n  const [openIndex, setOpenIndex] = useState<number | null>(null);\n\n  const faqs = [\n    {\n      question: \"How fast can you close?\",\n      answer: \"We can close in as little as 7 days or on your timeline.\"\n    },\n    {\n      question: \"Do I need to clean or make repairs?\",\n      answer: \"No. We buy houses as-is—no cleaning, no fixing.\"\n    },\n    {\n      question: \"Will I have to pay any fees or commissions?\",\n      answer: \"No. We cover all closing costs and there are zero agent fees.\"\n    },\n    {\n      question: \"How do you determine your offer price?\",\n      answer: \"We look at the condition, location, and recent sales to make a fair cash offer.\"\n    },\n    {\n      question: \"What types of houses do you buy?\",\n      answer: \"We buy houses in any condition—vacant, inherited, damaged, behind on payments, or with bad tenants.\"\n    },\n    {\n      question: \"What if I'm in foreclosure or behind on mortgage payments?\",\n      answer: \"We can work with you and the bank to stop foreclosure and buy your property fast.\"\n    },\n    {\n      question: \"Can I sell if I live out of state?\",\n      answer: \"Yes. We handle everything remotely and can close without you being here.\"\n    },\n    {\n      question: \"Is there any obligation if I get an offer?\",\n      answer: \"No obligation at all. Our offer is free and you decide if it's right for you.\"\n    },\n    {\n      question: \"What if I have tenants?\",\n      answer: \"We'll buy with tenants in place or help resolve any issues.\"\n    },\n    {\n      question: \"How do I get started?\",\n      answer: \"Just fill out the form or call us—our team will take it from there.\"\n    }\n  ];\n\n  const toggleFAQ = (index: number) => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n\n  return (\n    <section id=\"faq\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-blue-900 mb-4\">Frequently Asked Questions</h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            We're here to help answer your questions and make the process as smooth as possible for you.\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          {faqs.map((faq, index) => (\n            <div\n              key={index}\n              className=\"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden\"\n            >\n              <button\n                onClick={() => toggleFAQ(index)}\n                className=\"w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200\"\n              >\n                <h3 className=\"text-lg font-semibold text-blue-900 pr-4\">\n                  {faq.question}\n                </h3>\n                <div className=\"flex-shrink-0\">\n                  <svg\n                    className={`w-5 h-5 text-blue-600 transform transition-transform duration-200 ${\n                      openIndex === index ? 'rotate-180' : ''\n                    }`}\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M19 9l-7 7-7-7\"\n                    />\n                  </svg>\n                </div>\n              </button>\n              \n              {openIndex === index && (\n                <div className=\"px-6 pb-4\">\n                  <div className=\"border-t border-gray-200 pt-4\">\n                    <p className=\"text-gray-700 leading-relaxed\">\n                      {faq.answer}\n                    </p>\n                  </div>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n\n        <div className=\"mt-12 text-center\">\n          <div className=\"bg-blue-600 text-white p-8 rounded-lg\">\n            <h3 className=\"text-2xl font-bold mb-4\">Still Have Questions?</h3>\n            <p className=\"text-lg mb-6 opacity-90\">\n              We understand that selling your home is a big decision. Our team is here to provide \n              personalized guidance and support every step of the way.\n            </p>\n            <button\n              onClick={() => {\n                const element = document.getElementById('contact');\n                if (element) {\n                  element.scrollIntoView({ behavior: 'smooth' });\n                }\n              }}\n              className=\"bg-white text-blue-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200\"\n            >\n              Contact Us Today\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,YAAY,CAAC;QACjB,aAAa,cAAc,QAAQ,OAAO;IAC5C;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAM,WAAU;kBAC1B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;sDACX,IAAI,QAAQ;;;;;;sDAEf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,CAAC,kEAAkE,EAC5E,cAAc,QAAQ,eAAe,IACrC;gDACF,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gCAMT,cAAc,uBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDACV,IAAI,MAAM;;;;;;;;;;;;;;;;;2BAjCd;;;;;;;;;;8BA0CX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;0CAIvC,6LAAC;gCACC,SAAS;oCACP,MAAM,UAAU,SAAS,cAAc,CAAC;oCACxC,IAAI,SAAS;wCACX,QAAQ,cAAc,CAAC;4CAAE,UAAU;wCAAS;oCAC9C;gCACF;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAhIwB;KAAA", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/components/Contact.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\n\r\nexport default function Contact() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    phone: '',\r\n    propertyAddress: '',\r\n    message: '',\r\n    inquiryType: 'sell'\r\n  });\r\n\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitStatus, setSubmitStatus] = useState<{\r\n    type: 'success' | 'error' | null;\r\n    message: string;\r\n  }>({ type: null, message: '' });\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n    setSubmitStatus({ type: null, message: '' });\r\n\r\n    try {\r\n      const response = await fetch('/api/consultation', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(formData),\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        // Show success message\r\n        setSubmitStatus({\r\n          type: 'success',\r\n          message: 'Thank you for your inquiry! We will contact you within 24 hours.'\r\n        });\r\n        // Reset form\r\n        setFormData({\r\n          name: '',\r\n          email: '',\r\n          phone: '',\r\n          propertyAddress: '',\r\n          message: '',\r\n          inquiryType: 'sell'\r\n        });\r\n\r\n        // Show popup alert\r\n        alert('Thank you for your inquiry! We will contact you within 24 hours.');\r\n      } else {\r\n        setSubmitStatus({\r\n          type: 'error',\r\n          message: result.error || 'Something went wrong. Please try again.'\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error('Error submitting form:', error);\r\n      setSubmitStatus({\r\n        type: 'error',\r\n        message: 'Network error. Please check your connection and try again.'\r\n      });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  return (\r\n    <section id=\"contact\" className=\"py-20 bg-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl font-bold text-blue-900 mb-4\">Contact Us</h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n            Ready to get started? Contact us today for a free consultation and quick response.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\r\n          {/* Contact Form */}\r\n          <div className=\"bg-gray-50 p-8 rounded-lg\">\r\n            <h3 className=\"text-2xl font-bold text-blue-900 mb-6\">Get Your Free Consultation</h3>\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n              {/* Status Message */}\r\n              {submitStatus.type && (\r\n                <div className={`p-4 rounded-lg ${\r\n                  submitStatus.type === 'success'\r\n                    ? 'bg-green-50 border border-green-200 text-green-800'\r\n                    : 'bg-red-50 border border-red-200 text-red-800'\r\n                }`}>\r\n                  <div className=\"flex\">\r\n                    <div className=\"flex-shrink-0\">\r\n                      {submitStatus.type === 'success' ? (\r\n                        <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                        </svg>\r\n                      ) : (\r\n                        <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                        </svg>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"ml-3\">\r\n                      <p className=\"text-sm font-medium\">{submitStatus.message}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              <div>\r\n                <label htmlFor=\"inquiryType\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  I want to:\r\n                </label>\r\n                <select\r\n                  id=\"inquiryType\"\r\n                  name=\"inquiryType\"\r\n                  value={formData.inquiryType}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  required\r\n                >\r\n                  <option value=\"sell\">Sell my property</option>\r\n                  <option value=\"invest\">Explore investment opportunities</option>\r\n                  <option value=\"other\">Other inquiry</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Full Name *\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id=\"name\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    required\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Phone Number *\r\n                  </label>\r\n                  <input\r\n                    type=\"tel\"\r\n                    id=\"phone\"\r\n                    name=\"phone\"\r\n                    value={formData.phone}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Email Address *\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"propertyAddress\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Property Address (if applicable)\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"propertyAddress\"\r\n                  name=\"propertyAddress\"\r\n                  value={formData.propertyAddress}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  placeholder=\"123 Main St, Miami, FL 33101\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Additional Details\r\n                </label>\r\n                <textarea\r\n                  id=\"message\"\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  rows={4}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  placeholder=\"Tell us more about your needs...\"\r\n                />\r\n              </div>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                disabled={isSubmitting}\r\n                className={`w-full py-4 px-6 rounded-lg text-lg font-semibold transition-all duration-200 shadow-lg ${\r\n                  isSubmitting\r\n                    ? 'bg-gray-400 cursor-not-allowed'\r\n                    : 'bg-blue-600 text-white hover:bg-blue-700 transform hover:scale-105'\r\n                }`}\r\n              >\r\n                {isSubmitting ? (\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    Submitting...\r\n                  </div>\r\n                ) : (\r\n                  'Get My Free Consultation'\r\n                )}\r\n              </button>\r\n            </form>\r\n          </div>\r\n\r\n          {/* Contact Information */}\r\n          <div className=\"space-y-8\">\r\n            <div>\r\n              <h3 className=\"text-2xl font-bold text-blue-900 mb-6\">Get In Touch</h3>\r\n              <div className=\"space-y-6\">\r\n                <div className=\"flex items-start\">\r\n                  <svg className=\"w-6 h-6 text-blue-600 mr-4 mt-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\r\n                  </svg>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900\">Call Us</h4>\r\n                    <p className=\"text-gray-600\">(555) 123-4567</p>\r\n                    <p className=\"text-sm text-gray-500\">Available 7 days a week</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-start\">\r\n                  <svg className=\"w-6 h-6 text-blue-600 mr-4 mt-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                  </svg>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900\">Email Us</h4>\r\n                    <p className=\"text-gray-600\"><EMAIL></p>\r\n                    <p className=\"text-sm text-gray-500\">We respond within 24 hours</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-start\">\r\n                  <svg className=\"w-6 h-6 text-blue-600 mr-4 mt-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  </svg>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900\">Visit Us</h4>\r\n                    <p className=\"text-gray-600\">South Florida East Coast</p>\r\n                    <p className=\"text-sm text-gray-500\">Serving the entire region</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-blue-50 p-6 rounded-lg\">\r\n              <h4 className=\"text-xl font-bold text-blue-900 mb-3\">Why Choose Us?</h4>\r\n              <ul className=\"space-y-2 text-gray-700\">\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-4 h-4 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  Fast response within 24 hours\r\n                </li>\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-4 h-4 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  No obligation consultations\r\n                </li>\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-4 h-4 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  29+ years of proven experience\r\n                </li>\r\n                <li className=\"flex items-center\">\r\n                  <svg className=\"w-4 h-4 text-blue-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  Transparent and honest dealings\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,iBAAiB;QACjB,SAAS;QACT,aAAa;IACf;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QAAE,MAAM;QAAM,SAAS;IAAG;IAE7B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,gBAAgB;YAAE,MAAM;YAAM,SAAS;QAAG;QAE1C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,uBAAuB;gBACvB,gBAAgB;oBACd,MAAM;oBACN,SAAS;gBACX;gBACA,aAAa;gBACb,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP,OAAO;oBACP,iBAAiB;oBACjB,SAAS;oBACT,aAAa;gBACf;gBAEA,mBAAmB;gBACnB,MAAM;YACR,OAAO;gBACL,gBAAgB;oBACd,MAAM;oBACN,SAAS,OAAO,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,gBAAgB;gBACd,MAAM;gBACN,SAAS;YACX;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;wCAErC,aAAa,IAAI,kBAChB,6LAAC;4CAAI,WAAW,CAAC,eAAe,EAC9B,aAAa,IAAI,KAAK,YAClB,uDACA,gDACJ;sDACA,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,aAAa,IAAI,KAAK,0BACrB,6LAAC;4DAAI,WAAU;4DAAyB,SAAQ;4DAAY,MAAK;sEAC/D,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAwI,UAAS;;;;;;;;;;iFAG9K,6LAAC;4DAAI,WAAU;4DAAuB,SAAQ;4DAAY,MAAK;sEAC7D,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAA0N,UAAS;;;;;;;;;;;;;;;;kEAIpQ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;sEAAuB,aAAa,OAAO;;;;;;;;;;;;;;;;;;;;;;sDAMhE,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAA+C;;;;;;8DAGtF,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,WAAU;oDACV,QAAQ;;sEAER,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;sDAI1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA+C;;;;;;sEAG/E,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAA+C;;;;;;8DAG1F,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,eAAe;oDAC/B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,wFAAwF,EAClG,eACI,mCACA,sEACJ;sDAED,6BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;;;;;uDAIR;;;;;;;;;;;;;;;;;;sCAOR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;4DAAkC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACzF,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;4DAAkC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACzF,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;4DAAkC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACzF,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;sEAEvE,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;wDACrJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GArTwB;KAAA", "debugId": null}}, {"offset": {"line": 2154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/components/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nexport default function Footer() {\r\n  return (\r\n    <footer className=\"bg-blue-900 text-white py-12\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n          {/* Company Info */}\r\n          <div>\r\n            <h3 className=\"text-2xl font-bold mb-4\">Residential Rehab</h3>\r\n            <p className=\"text-blue-200 mb-4\">\r\nWe&apos;re a trusted real estate company focused on helping property owners find fair, stress-free solutions whether you're ready to sell, need guidance, or simply exploring your options.\r\n            </p>\r\n            <p className=\"text-blue-200\">\r\n              Trusted expertise. Proven results. Your success is our priority.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Quick Links */}\r\n          <div>\r\n            <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\r\n            <ul className=\"space-y-2\">\r\n              <li>\r\n                <button\r\n                  onClick={() => {\r\n                    const element = document.getElementById('home');\r\n                    if (element) {\r\n                      element.scrollIntoView({ behavior: 'smooth' });\r\n                    }\r\n                  }}\r\n                  className=\"text-blue-200 hover:text-white transition-colors duration-200\"\r\n                >\r\n                  Home\r\n                </button>\r\n              </li>\r\n              <li>\r\n                <button\r\n                  onClick={() => {\r\n                    const element = document.getElementById('about');\r\n                    if (element) {\r\n                      element.scrollIntoView({ behavior: 'smooth' });\r\n                    }\r\n                  }}\r\n                  className=\"text-blue-200 hover:text-white transition-colors duration-200\"\r\n                >\r\n                  About Us\r\n                </button>\r\n              </li>\r\n              <li>\r\n                <button\r\n                  onClick={() => {\r\n                    const element = document.getElementById('services');\r\n                    if (element) {\r\n                      element.scrollIntoView({ behavior: 'smooth' });\r\n                    }\r\n                  }}\r\n                  className=\"text-blue-200 hover:text-white transition-colors duration-200\"\r\n                >\r\n                  What We Do\r\n                </button>\r\n              </li>\r\n              <li>\r\n                <button\r\n                  onClick={() => {\r\n                    const element = document.getElementById('faq');\r\n                    if (element) {\r\n                      element.scrollIntoView({ behavior: 'smooth' });\r\n                    }\r\n                  }}\r\n                  className=\"text-blue-200 hover:text-white transition-colors duration-200\"\r\n                >\r\n                  FAQ\r\n                </button>\r\n              </li>\r\n              <li>\r\n                <button\r\n                  onClick={() => {\r\n                    const element = document.getElementById('contact');\r\n                    if (element) {\r\n                      element.scrollIntoView({ behavior: 'smooth' });\r\n                    }\r\n                  }}\r\n                  className=\"text-blue-200 hover:text-white transition-colors duration-200\"\r\n                >\r\n                  Contact Us\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Contact Info */}\r\n          <div>\r\n            <h4 className=\"text-lg font-semibold mb-4\">Contact Information</h4>\r\n            <div className=\"space-y-3\">\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"w-5 h-5 text-blue-400 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\r\n                </svg>\r\n                <span className=\"text-blue-200\">(555) 123-4567</span>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"w-5 h-5 text-blue-400 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                </svg>\r\n                <span className=\"text-blue-200\"><EMAIL></span>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"w-5 h-5 text-blue-400 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                </svg>\r\n                <span className=\"text-blue-200\">South Florida East Coast</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"border-t border-blue-800 mt-8 pt-8 text-center\">\r\n          <p className=\"text-blue-200\">\r\n            © 2024 Presidential Real Estate Holdings. All rights reserved. | \r\n            Serving South Florida since 1996\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAM/B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC;gDACC,SAAS;oDACP,MAAM,UAAU,SAAS,cAAc,CAAC;oDACxC,IAAI,SAAS;wDACX,QAAQ,cAAc,CAAC;4DAAE,UAAU;wDAAS;oDAC9C;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;sDACC,cAAA,6LAAC;gDACC,SAAS;oDACP,MAAM,UAAU,SAAS,cAAc,CAAC;oDACxC,IAAI,SAAS;wDACX,QAAQ,cAAc,CAAC;4DAAE,UAAU;wDAAS;oDAC9C;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;sDACC,cAAA,6LAAC;gDACC,SAAS;oDACP,MAAM,UAAU,SAAS,cAAc,CAAC;oDACxC,IAAI,SAAS;wDACX,QAAQ,cAAc,CAAC;4DAAE,UAAU;wDAAS;oDAC9C;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;sDACC,cAAA,6LAAC;gDACC,SAAS;oDACP,MAAM,UAAU,SAAS,cAAc,CAAC;oDACxC,IAAI,SAAS;wDACX,QAAQ,cAAc,CAAC;4DAAE,UAAU;wDAAS;oDAC9C;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC;sDACC,cAAA,6LAAC;gDACC,SAAS;oDACP,MAAM,UAAU,SAAS,cAAc,CAAC;oDACxC,IAAI,SAAS;wDACX,QAAQ,cAAc,CAAC;4DAAE,UAAU;wDAAS;oDAC9C;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAA6B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACpF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAA6B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACpF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAA6B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;;sEACpF,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAQvC;KA5HwB", "debugId": null}}]}