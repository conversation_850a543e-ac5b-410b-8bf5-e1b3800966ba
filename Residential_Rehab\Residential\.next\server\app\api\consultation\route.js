/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/consultation/route";
exports.ids = ["app/api/consultation/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconsultation%2Froute&page=%2Fapi%2Fconsultation%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconsultation%2Froute.js&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconsultation%2Froute&page=%2Fapi%2Fconsultation%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconsultation%2Froute.js&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DELL_Desktop_Residential_Rehab_Residential_Rehab_Residential_src_app_api_consultation_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/consultation/route.js */ \"(rsc)/./src/app/api/consultation/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/consultation/route\",\n        pathname: \"/api/consultation\",\n        filename: \"route\",\n        bundlePath: \"app/api/consultation/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Residential Rehab\\\\Residential_Rehab\\\\Residential\\\\src\\\\app\\\\api\\\\consultation\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Desktop_Residential_Rehab_Residential_Rehab_Residential_src_app_api_consultation_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconsultation%2Froute&page=%2Fapi%2Fconsultation%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconsultation%2Froute.js&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/consultation/route.js":
/*!*******************************************!*\
  !*** ./src/app/api/consultation/route.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.js\");\n/* harmony import */ var _models_Consultation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/Consultation */ \"(rsc)/./src/models/Consultation.js\");\n\n\n\n// POST - Create a new consultation request\nasync function POST(request) {\n    try {\n        // Connect to database\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Parse request body\n        const body = await request.json();\n        const { name, email, phone, propertyAddress, inquiryType, message } = body;\n        // Validate required fields\n        if (!name || !email || !phone || !inquiryType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing required fields: name, email, phone, and inquiry type are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate email format\n        const emailRegex = /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/;\n        if (!emailRegex.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Please provide a valid email address'\n            }, {\n                status: 400\n            });\n        }\n        // Validate inquiry type\n        const validInquiryTypes = [\n            'sell',\n            'invest',\n            'other'\n        ];\n        if (!validInquiryTypes.includes(inquiryType)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid inquiry type'\n            }, {\n                status: 400\n            });\n        }\n        // Create new consultation record\n        const consultation = new _models_Consultation__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            name: name.trim(),\n            email: email.trim().toLowerCase(),\n            phone: phone.trim(),\n            propertyAddress: propertyAddress ? propertyAddress.trim() : '',\n            inquiryType,\n            message: message ? message.trim() : '',\n            status: 'new'\n        });\n        // Save to database\n        const savedConsultation = await consultation.save();\n        // Return success response\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Consultation request submitted successfully',\n            data: {\n                id: savedConsultation._id,\n                name: savedConsultation.name,\n                email: savedConsultation.email,\n                inquiryType: savedConsultation.inquiryType,\n                createdAt: savedConsultation.createdAt\n            }\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating consultation:', error);\n        // Handle mongoose validation errors\n        if (error.name === 'ValidationError') {\n            const validationErrors = Object.values(error.errors).map((err)=>err.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Validation failed',\n                details: validationErrors\n            }, {\n                status: 400\n            });\n        }\n        // Handle duplicate email error\n        if (error.code === 11000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'A consultation request with this email already exists'\n            }, {\n                status: 409\n            });\n        }\n        // Handle other errors\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error. Please try again later.'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - Retrieve consultation requests (for admin use)\nasync function GET(request) {\n    try {\n        // Connect to database\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page')) || 1;\n        const limit = parseInt(searchParams.get('limit')) || 10;\n        const status = searchParams.get('status');\n        const inquiryType = searchParams.get('inquiryType');\n        // Build query\n        const query = {};\n        if (status) query.status = status;\n        if (inquiryType) query.inquiryType = inquiryType;\n        // Calculate skip value for pagination\n        const skip = (page - 1) * limit;\n        // Get consultations with pagination\n        const consultations = await _models_Consultation__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find(query).sort({\n            createdAt: -1\n        }).skip(skip).limit(limit).select('-__v');\n        // Get total count for pagination\n        const total = await _models_Consultation__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(query);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: consultations,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching consultations:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jb25zdWx0YXRpb24vcm91dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMkM7QUFDTDtBQUNXO0FBRWpELDJDQUEyQztBQUNwQyxlQUFlRyxLQUFLQyxPQUFPO0lBQ2hDLElBQUk7UUFDRixzQkFBc0I7UUFDdEIsTUFBTUgsd0RBQVNBO1FBRWYscUJBQXFCO1FBQ3JCLE1BQU1JLE9BQU8sTUFBTUQsUUFBUUUsSUFBSTtRQUMvQixNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUVDLGVBQWUsRUFBRUMsV0FBVyxFQUFFQyxPQUFPLEVBQUUsR0FBR1A7UUFFdEUsMkJBQTJCO1FBQzNCLElBQUksQ0FBQ0UsUUFBUSxDQUFDQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ0UsYUFBYTtZQUM3QyxPQUFPWCxxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFDRU8sU0FBUztnQkFDVEMsT0FBTztZQUNULEdBQ0E7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHdCQUF3QjtRQUN4QixNQUFNQyxhQUFhO1FBQ25CLElBQUksQ0FBQ0EsV0FBV0MsSUFBSSxDQUFDVCxRQUFRO1lBQzNCLE9BQU9SLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO2dCQUNFTyxTQUFTO2dCQUNUQyxPQUFPO1lBQ1QsR0FDQTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsd0JBQXdCO1FBQ3hCLE1BQU1HLG9CQUFvQjtZQUFDO1lBQVE7WUFBVTtTQUFRO1FBQ3JELElBQUksQ0FBQ0Esa0JBQWtCQyxRQUFRLENBQUNSLGNBQWM7WUFDNUMsT0FBT1gscURBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQ0VPLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVCxHQUNBO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxpQ0FBaUM7UUFDakMsTUFBTUssZUFBZSxJQUFJbEIsNERBQVlBLENBQUM7WUFDcENLLE1BQU1BLEtBQUtjLElBQUk7WUFDZmIsT0FBT0EsTUFBTWEsSUFBSSxHQUFHQyxXQUFXO1lBQy9CYixPQUFPQSxNQUFNWSxJQUFJO1lBQ2pCWCxpQkFBaUJBLGtCQUFrQkEsZ0JBQWdCVyxJQUFJLEtBQUs7WUFDNURWO1lBQ0FDLFNBQVNBLFVBQVVBLFFBQVFTLElBQUksS0FBSztZQUNwQ04sUUFBUTtRQUNWO1FBRUEsbUJBQW1CO1FBQ25CLE1BQU1RLG9CQUFvQixNQUFNSCxhQUFhSSxJQUFJO1FBRWpELDBCQUEwQjtRQUMxQixPQUFPeEIscURBQVlBLENBQUNNLElBQUksQ0FDdEI7WUFDRU8sU0FBUztZQUNURCxTQUFTO1lBQ1RhLE1BQU07Z0JBQ0pDLElBQUlILGtCQUFrQkksR0FBRztnQkFDekJwQixNQUFNZ0Isa0JBQWtCaEIsSUFBSTtnQkFDNUJDLE9BQU9lLGtCQUFrQmYsS0FBSztnQkFDOUJHLGFBQWFZLGtCQUFrQlosV0FBVztnQkFDMUNpQixXQUFXTCxrQkFBa0JLLFNBQVM7WUFDeEM7UUFDRixHQUNBO1lBQUViLFFBQVE7UUFBSTtJQUdsQixFQUFFLE9BQU9ELE9BQU87UUFDZGUsUUFBUWYsS0FBSyxDQUFDLGdDQUFnQ0E7UUFFOUMsb0NBQW9DO1FBQ3BDLElBQUlBLE1BQU1QLElBQUksS0FBSyxtQkFBbUI7WUFDcEMsTUFBTXVCLG1CQUFtQkMsT0FBT0MsTUFBTSxDQUFDbEIsTUFBTW1CLE1BQU0sRUFBRUMsR0FBRyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJdkIsT0FBTztZQUMzRSxPQUFPWixxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFDRU8sU0FBUztnQkFDVEMsT0FBTztnQkFDUHNCLFNBQVNOO1lBQ1gsR0FDQTtnQkFBRWYsUUFBUTtZQUFJO1FBRWxCO1FBRUEsK0JBQStCO1FBQy9CLElBQUlELE1BQU11QixJQUFJLEtBQUssT0FBTztZQUN4QixPQUFPckMscURBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQ0VPLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVCxHQUNBO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxzQkFBc0I7UUFDdEIsT0FBT2YscURBQVlBLENBQUNNLElBQUksQ0FDdEI7WUFDRU8sU0FBUztZQUNUQyxPQUFPO1FBQ1QsR0FDQTtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLHVEQUF1RDtBQUNoRCxlQUFldUIsSUFBSWxDLE9BQU87SUFDL0IsSUFBSTtRQUNGLHNCQUFzQjtRQUN0QixNQUFNSCx3REFBU0E7UUFFZix1QkFBdUI7UUFDdkIsTUFBTSxFQUFFc0MsWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSXBDLFFBQVFxQyxHQUFHO1FBQzVDLE1BQU1DLE9BQU9DLFNBQVNKLGFBQWFLLEdBQUcsQ0FBQyxZQUFZO1FBQ25ELE1BQU1DLFFBQVFGLFNBQVNKLGFBQWFLLEdBQUcsQ0FBQyxhQUFhO1FBQ3JELE1BQU03QixTQUFTd0IsYUFBYUssR0FBRyxDQUFDO1FBQ2hDLE1BQU1qQyxjQUFjNEIsYUFBYUssR0FBRyxDQUFDO1FBRXJDLGNBQWM7UUFDZCxNQUFNRSxRQUFRLENBQUM7UUFDZixJQUFJL0IsUUFBUStCLE1BQU0vQixNQUFNLEdBQUdBO1FBQzNCLElBQUlKLGFBQWFtQyxNQUFNbkMsV0FBVyxHQUFHQTtRQUVyQyxzQ0FBc0M7UUFDdEMsTUFBTW9DLE9BQU8sQ0FBQ0wsT0FBTyxLQUFLRztRQUUxQixvQ0FBb0M7UUFDcEMsTUFBTUcsZ0JBQWdCLE1BQU05Qyw0REFBWUEsQ0FBQytDLElBQUksQ0FBQ0gsT0FDM0NJLElBQUksQ0FBQztZQUFFdEIsV0FBVyxDQUFDO1FBQUUsR0FDckJtQixJQUFJLENBQUNBLE1BQ0xGLEtBQUssQ0FBQ0EsT0FDTk0sTUFBTSxDQUFDO1FBRVYsaUNBQWlDO1FBQ2pDLE1BQU1DLFFBQVEsTUFBTWxELDREQUFZQSxDQUFDbUQsY0FBYyxDQUFDUDtRQUVoRCxPQUFPOUMscURBQVlBLENBQUNNLElBQUksQ0FBQztZQUN2Qk8sU0FBUztZQUNUWSxNQUFNdUI7WUFDTk0sWUFBWTtnQkFDVlo7Z0JBQ0FHO2dCQUNBTztnQkFDQUcsT0FBT0MsS0FBS0MsSUFBSSxDQUFDTCxRQUFRUDtZQUMzQjtRQUNGO0lBRUYsRUFBRSxPQUFPL0IsT0FBTztRQUNkZSxRQUFRZixLQUFLLENBQUMsaUNBQWlDQTtRQUMvQyxPQUFPZCxxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtZQUNFTyxTQUFTO1lBQ1RDLE9BQU87UUFDVCxHQUNBO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERFTExcXERlc2t0b3BcXFJlc2lkZW50aWFsIFJlaGFiXFxSZXNpZGVudGlhbF9SZWhhYlxcUmVzaWRlbnRpYWxcXHNyY1xcYXBwXFxhcGlcXGNvbnN1bHRhdGlvblxccm91dGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IGNvbm5lY3REQiBmcm9tICdAL2xpYi9tb25nb2RiJztcbmltcG9ydCBDb25zdWx0YXRpb24gZnJvbSAnQC9tb2RlbHMvQ29uc3VsdGF0aW9uJztcblxuLy8gUE9TVCAtIENyZWF0ZSBhIG5ldyBjb25zdWx0YXRpb24gcmVxdWVzdFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdCkge1xuICB0cnkge1xuICAgIC8vIENvbm5lY3QgdG8gZGF0YWJhc2VcbiAgICBhd2FpdCBjb25uZWN0REIoKTtcblxuICAgIC8vIFBhcnNlIHJlcXVlc3QgYm9keVxuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBjb25zdCB7IG5hbWUsIGVtYWlsLCBwaG9uZSwgcHJvcGVydHlBZGRyZXNzLCBpbnF1aXJ5VHlwZSwgbWVzc2FnZSB9ID0gYm9keTtcblxuICAgIC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuICAgIGlmICghbmFtZSB8fCAhZW1haWwgfHwgIXBob25lIHx8ICFpbnF1aXJ5VHlwZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IFxuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLCBcbiAgICAgICAgICBlcnJvcjogJ01pc3NpbmcgcmVxdWlyZWQgZmllbGRzOiBuYW1lLCBlbWFpbCwgcGhvbmUsIGFuZCBpbnF1aXJ5IHR5cGUgYXJlIHJlcXVpcmVkJyBcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGVtYWlsIGZvcm1hdFxuICAgIGNvbnN0IGVtYWlsUmVnZXggPSAvXlxcdysoWy4tXT9cXHcrKSpAXFx3KyhbLi1dP1xcdyspKihcXC5cXHd7MiwzfSkrJC87XG4gICAgaWYgKCFlbWFpbFJlZ2V4LnRlc3QoZW1haWwpKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgXG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsIFxuICAgICAgICAgIGVycm9yOiAnUGxlYXNlIHByb3ZpZGUgYSB2YWxpZCBlbWFpbCBhZGRyZXNzJyBcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGlucXVpcnkgdHlwZVxuICAgIGNvbnN0IHZhbGlkSW5xdWlyeVR5cGVzID0gWydzZWxsJywgJ2ludmVzdCcsICdvdGhlciddO1xuICAgIGlmICghdmFsaWRJbnF1aXJ5VHlwZXMuaW5jbHVkZXMoaW5xdWlyeVR5cGUpKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgXG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsIFxuICAgICAgICAgIGVycm9yOiAnSW52YWxpZCBpbnF1aXJ5IHR5cGUnIFxuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIG5ldyBjb25zdWx0YXRpb24gcmVjb3JkXG4gICAgY29uc3QgY29uc3VsdGF0aW9uID0gbmV3IENvbnN1bHRhdGlvbih7XG4gICAgICBuYW1lOiBuYW1lLnRyaW0oKSxcbiAgICAgIGVtYWlsOiBlbWFpbC50cmltKCkudG9Mb3dlckNhc2UoKSxcbiAgICAgIHBob25lOiBwaG9uZS50cmltKCksXG4gICAgICBwcm9wZXJ0eUFkZHJlc3M6IHByb3BlcnR5QWRkcmVzcyA/IHByb3BlcnR5QWRkcmVzcy50cmltKCkgOiAnJyxcbiAgICAgIGlucXVpcnlUeXBlLFxuICAgICAgbWVzc2FnZTogbWVzc2FnZSA/IG1lc3NhZ2UudHJpbSgpIDogJycsXG4gICAgICBzdGF0dXM6ICduZXcnXG4gICAgfSk7XG5cbiAgICAvLyBTYXZlIHRvIGRhdGFiYXNlXG4gICAgY29uc3Qgc2F2ZWRDb25zdWx0YXRpb24gPSBhd2FpdCBjb25zdWx0YXRpb24uc2F2ZSgpO1xuXG4gICAgLy8gUmV0dXJuIHN1Y2Nlc3MgcmVzcG9uc2VcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIG1lc3NhZ2U6ICdDb25zdWx0YXRpb24gcmVxdWVzdCBzdWJtaXR0ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIGlkOiBzYXZlZENvbnN1bHRhdGlvbi5faWQsXG4gICAgICAgICAgbmFtZTogc2F2ZWRDb25zdWx0YXRpb24ubmFtZSxcbiAgICAgICAgICBlbWFpbDogc2F2ZWRDb25zdWx0YXRpb24uZW1haWwsXG4gICAgICAgICAgaW5xdWlyeVR5cGU6IHNhdmVkQ29uc3VsdGF0aW9uLmlucXVpcnlUeXBlLFxuICAgICAgICAgIGNyZWF0ZWRBdDogc2F2ZWRDb25zdWx0YXRpb24uY3JlYXRlZEF0XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogMjAxIH1cbiAgICApO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgY29uc3VsdGF0aW9uOicsIGVycm9yKTtcblxuICAgIC8vIEhhbmRsZSBtb25nb29zZSB2YWxpZGF0aW9uIGVycm9yc1xuICAgIGlmIChlcnJvci5uYW1lID09PSAnVmFsaWRhdGlvbkVycm9yJykge1xuICAgICAgY29uc3QgdmFsaWRhdGlvbkVycm9ycyA9IE9iamVjdC52YWx1ZXMoZXJyb3IuZXJyb3JzKS5tYXAoZXJyID0+IGVyci5tZXNzYWdlKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSwgXG4gICAgICAgICAgZXJyb3I6ICdWYWxpZGF0aW9uIGZhaWxlZCcsXG4gICAgICAgICAgZGV0YWlsczogdmFsaWRhdGlvbkVycm9yc1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gSGFuZGxlIGR1cGxpY2F0ZSBlbWFpbCBlcnJvclxuICAgIGlmIChlcnJvci5jb2RlID09PSAxMTAwMCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IFxuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLCBcbiAgICAgICAgICBlcnJvcjogJ0EgY29uc3VsdGF0aW9uIHJlcXVlc3Qgd2l0aCB0aGlzIGVtYWlsIGFscmVhZHkgZXhpc3RzJyBcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwOSB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEhhbmRsZSBvdGhlciBlcnJvcnNcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IFxuICAgICAgICBzdWNjZXNzOiBmYWxzZSwgXG4gICAgICAgIGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicgXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyBHRVQgLSBSZXRyaWV2ZSBjb25zdWx0YXRpb24gcmVxdWVzdHMgKGZvciBhZG1pbiB1c2UpXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICAvLyBDb25uZWN0IHRvIGRhdGFiYXNlXG4gICAgYXdhaXQgY29ubmVjdERCKCk7XG5cbiAgICAvLyBHZXQgcXVlcnkgcGFyYW1ldGVyc1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBjb25zdCBwYWdlID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldCgncGFnZScpKSB8fCAxO1xuICAgIGNvbnN0IGxpbWl0ID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldCgnbGltaXQnKSkgfHwgMTA7XG4gICAgY29uc3Qgc3RhdHVzID0gc2VhcmNoUGFyYW1zLmdldCgnc3RhdHVzJyk7XG4gICAgY29uc3QgaW5xdWlyeVR5cGUgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpbnF1aXJ5VHlwZScpO1xuXG4gICAgLy8gQnVpbGQgcXVlcnlcbiAgICBjb25zdCBxdWVyeSA9IHt9O1xuICAgIGlmIChzdGF0dXMpIHF1ZXJ5LnN0YXR1cyA9IHN0YXR1cztcbiAgICBpZiAoaW5xdWlyeVR5cGUpIHF1ZXJ5LmlucXVpcnlUeXBlID0gaW5xdWlyeVR5cGU7XG5cbiAgICAvLyBDYWxjdWxhdGUgc2tpcCB2YWx1ZSBmb3IgcGFnaW5hdGlvblxuICAgIGNvbnN0IHNraXAgPSAocGFnZSAtIDEpICogbGltaXQ7XG5cbiAgICAvLyBHZXQgY29uc3VsdGF0aW9ucyB3aXRoIHBhZ2luYXRpb25cbiAgICBjb25zdCBjb25zdWx0YXRpb25zID0gYXdhaXQgQ29uc3VsdGF0aW9uLmZpbmQocXVlcnkpXG4gICAgICAuc29ydCh7IGNyZWF0ZWRBdDogLTEgfSlcbiAgICAgIC5za2lwKHNraXApXG4gICAgICAubGltaXQobGltaXQpXG4gICAgICAuc2VsZWN0KCctX192Jyk7XG5cbiAgICAvLyBHZXQgdG90YWwgY291bnQgZm9yIHBhZ2luYXRpb25cbiAgICBjb25zdCB0b3RhbCA9IGF3YWl0IENvbnN1bHRhdGlvbi5jb3VudERvY3VtZW50cyhxdWVyeSk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IGNvbnN1bHRhdGlvbnMsXG4gICAgICBwYWdpbmF0aW9uOiB7XG4gICAgICAgIHBhZ2UsXG4gICAgICAgIGxpbWl0LFxuICAgICAgICB0b3RhbCxcbiAgICAgICAgcGFnZXM6IE1hdGguY2VpbCh0b3RhbCAvIGxpbWl0KVxuICAgICAgfVxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY29uc3VsdGF0aW9uczonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBcbiAgICAgICAgc3VjY2VzczogZmFsc2UsIFxuICAgICAgICBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicgXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNvbm5lY3REQiIsIkNvbnN1bHRhdGlvbiIsIlBPU1QiLCJyZXF1ZXN0IiwiYm9keSIsImpzb24iLCJuYW1lIiwiZW1haWwiLCJwaG9uZSIsInByb3BlcnR5QWRkcmVzcyIsImlucXVpcnlUeXBlIiwibWVzc2FnZSIsInN1Y2Nlc3MiLCJlcnJvciIsInN0YXR1cyIsImVtYWlsUmVnZXgiLCJ0ZXN0IiwidmFsaWRJbnF1aXJ5VHlwZXMiLCJpbmNsdWRlcyIsImNvbnN1bHRhdGlvbiIsInRyaW0iLCJ0b0xvd2VyQ2FzZSIsInNhdmVkQ29uc3VsdGF0aW9uIiwic2F2ZSIsImRhdGEiLCJpZCIsIl9pZCIsImNyZWF0ZWRBdCIsImNvbnNvbGUiLCJ2YWxpZGF0aW9uRXJyb3JzIiwiT2JqZWN0IiwidmFsdWVzIiwiZXJyb3JzIiwibWFwIiwiZXJyIiwiZGV0YWlscyIsImNvZGUiLCJHRVQiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJwYWdlIiwicGFyc2VJbnQiLCJnZXQiLCJsaW1pdCIsInF1ZXJ5Iiwic2tpcCIsImNvbnN1bHRhdGlvbnMiLCJmaW5kIiwic29ydCIsInNlbGVjdCIsInRvdGFsIiwiY291bnREb2N1bWVudHMiLCJwYWdpbmF0aW9uIiwicGFnZXMiLCJNYXRoIiwiY2VpbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/consultation/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.js":
/*!****************************!*\
  !*** ./src/lib/mongodb.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21vbmdvZGIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBRWhDLE1BQU1DLGNBQWNDLFFBQVFDLEdBQUcsQ0FBQ0YsV0FBVztBQUUzQyxJQUFJLENBQUNBLGFBQWE7SUFDaEIsTUFBTSxJQUFJRyxNQUFNO0FBQ2xCO0FBRUE7Ozs7Q0FJQyxHQUNELElBQUlDLFNBQVNDLE9BQU9OLFFBQVE7QUFFNUIsSUFBSSxDQUFDSyxRQUFRO0lBQ1hBLFNBQVNDLE9BQU9OLFFBQVEsR0FBRztRQUFFTyxNQUFNO1FBQU1DLFNBQVM7SUFBSztBQUN6RDtBQUVBLGVBQWVDO0lBQ2IsSUFBSUosT0FBT0UsSUFBSSxFQUFFO1FBQ2YsT0FBT0YsT0FBT0UsSUFBSTtJQUNwQjtJQUVBLElBQUksQ0FBQ0YsT0FBT0csT0FBTyxFQUFFO1FBQ25CLE1BQU1FLE9BQU87WUFDWEMsZ0JBQWdCO1FBQ2xCO1FBRUFOLE9BQU9HLE9BQU8sR0FBR1IsdURBQWdCLENBQUNDLGFBQWFTLE1BQU1HLElBQUksQ0FBQyxDQUFDYjtZQUN6RCxPQUFPQTtRQUNUO0lBQ0Y7SUFFQSxJQUFJO1FBQ0ZLLE9BQU9FLElBQUksR0FBRyxNQUFNRixPQUFPRyxPQUFPO0lBQ3BDLEVBQUUsT0FBT00sR0FBRztRQUNWVCxPQUFPRyxPQUFPLEdBQUc7UUFDakIsTUFBTU07SUFDUjtJQUVBLE9BQU9ULE9BQU9FLElBQUk7QUFDcEI7QUFFQSxpRUFBZUUsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxERUxMXFxEZXNrdG9wXFxSZXNpZGVudGlhbCBSZWhhYlxcUmVzaWRlbnRpYWxfUmVoYWJcXFJlc2lkZW50aWFsXFxzcmNcXGxpYlxcbW9uZ29kYi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSAnbW9uZ29vc2UnO1xuXG5jb25zdCBNT05HT0RCX1VSSSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJO1xuXG5pZiAoIU1PTkdPREJfVVJJKSB7XG4gIHRocm93IG5ldyBFcnJvcignUGxlYXNlIGRlZmluZSB0aGUgTU9OR09EQl9VUkkgZW52aXJvbm1lbnQgdmFyaWFibGUgaW5zaWRlIC5lbnYubG9jYWwnKTtcbn1cblxuLyoqXG4gKiBHbG9iYWwgaXMgdXNlZCBoZXJlIHRvIG1haW50YWluIGEgY2FjaGVkIGNvbm5lY3Rpb24gYWNyb3NzIGhvdCByZWxvYWRzXG4gKiBpbiBkZXZlbG9wbWVudC4gVGhpcyBwcmV2ZW50cyBjb25uZWN0aW9ucyBncm93aW5nIGV4cG9uZW50aWFsbHlcbiAqIGR1cmluZyBBUEkgUm91dGUgdXNhZ2UuXG4gKi9cbmxldCBjYWNoZWQgPSBnbG9iYWwubW9uZ29vc2U7XG5cbmlmICghY2FjaGVkKSB7XG4gIGNhY2hlZCA9IGdsb2JhbC5tb25nb29zZSA9IHsgY29ubjogbnVsbCwgcHJvbWlzZTogbnVsbCB9O1xufVxuXG5hc3luYyBmdW5jdGlvbiBjb25uZWN0REIoKSB7XG4gIGlmIChjYWNoZWQuY29ubikge1xuICAgIHJldHVybiBjYWNoZWQuY29ubjtcbiAgfVxuXG4gIGlmICghY2FjaGVkLnByb21pc2UpIHtcbiAgICBjb25zdCBvcHRzID0ge1xuICAgICAgYnVmZmVyQ29tbWFuZHM6IGZhbHNlLFxuICAgIH07XG5cbiAgICBjYWNoZWQucHJvbWlzZSA9IG1vbmdvb3NlLmNvbm5lY3QoTU9OR09EQl9VUkksIG9wdHMpLnRoZW4oKG1vbmdvb3NlKSA9PiB7XG4gICAgICByZXR1cm4gbW9uZ29vc2U7XG4gICAgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIGNhY2hlZC5jb25uID0gYXdhaXQgY2FjaGVkLnByb21pc2U7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBjYWNoZWQucHJvbWlzZSA9IG51bGw7XG4gICAgdGhyb3cgZTtcbiAgfVxuXG4gIHJldHVybiBjYWNoZWQuY29ubjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgY29ubmVjdERCO1xuIl0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiTU9OR09EQl9VUkkiLCJwcm9jZXNzIiwiZW52IiwiRXJyb3IiLCJjYWNoZWQiLCJnbG9iYWwiLCJjb25uIiwicHJvbWlzZSIsImNvbm5lY3REQiIsIm9wdHMiLCJidWZmZXJDb21tYW5kcyIsImNvbm5lY3QiLCJ0aGVuIiwiZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./src/models/Consultation.js":
/*!************************************!*\
  !*** ./src/models/Consultation.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConsultationSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Name cannot be more than 100 characters'\n        ]\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            'Email is required'\n        ],\n        trim: true,\n        lowercase: true,\n        match: [\n            /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n            'Please enter a valid email'\n        ]\n    },\n    phone: {\n        type: String,\n        required: [\n            true,\n            'Phone number is required'\n        ],\n        trim: true,\n        maxlength: [\n            20,\n            'Phone number cannot be more than 20 characters'\n        ]\n    },\n    propertyAddress: {\n        type: String,\n        trim: true,\n        maxlength: [\n            200,\n            'Property address cannot be more than 200 characters'\n        ]\n    },\n    inquiryType: {\n        type: String,\n        required: [\n            true,\n            'Inquiry type is required'\n        ],\n        enum: [\n            'sell',\n            'invest',\n            'other'\n        ],\n        default: 'sell'\n    },\n    message: {\n        type: String,\n        trim: true,\n        maxlength: [\n            1000,\n            'Message cannot be more than 1000 characters'\n        ]\n    },\n    status: {\n        type: String,\n        enum: [\n            'new',\n            'contacted',\n            'in-progress',\n            'completed'\n        ],\n        default: 'new'\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Update the updatedAt field before saving\nConsultationSchema.pre('save', function(next) {\n    this.updatedAt = Date.now();\n    next();\n});\n// Create indexes for better query performance\nConsultationSchema.index({\n    email: 1\n});\nConsultationSchema.index({\n    createdAt: -1\n});\nConsultationSchema.index({\n    status: 1\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Consultation || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Consultation', ConsultationSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Consultation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconsultation%2Froute&page=%2Fapi%2Fconsultation%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconsultation%2Froute.js&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDesktop%5CResidential%20Rehab%5CResidential_Rehab%5CResidential&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();