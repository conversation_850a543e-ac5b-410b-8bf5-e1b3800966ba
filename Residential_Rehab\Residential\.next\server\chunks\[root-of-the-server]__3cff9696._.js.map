{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/models/Consultation.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst ConsultationSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot be more than 100 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    trim: true,\n    lowercase: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  phone: {\n    type: String,\n    required: [true, 'Phone number is required'],\n    trim: true,\n    maxlength: [20, 'Phone number cannot be more than 20 characters']\n  },\n  propertyAddress: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Property address cannot be more than 200 characters']\n  },\n  inquiryType: {\n    type: String,\n    required: [true, 'Inquiry type is required'],\n    enum: ['sell', 'invest', 'other'],\n    default: 'sell'\n  },\n  message: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Message cannot be more than 1000 characters']\n  },\n  status: {\n    type: String,\n    enum: ['new', 'contacted', 'in-progress', 'completed'],\n    default: 'new'\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  },\n  updatedAt: {\n    type: Date,\n    default: Date.now\n  }\n});\n\n// Update the updatedAt field before saving\nConsultationSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\n// Create indexes for better query performance\nConsultationSchema.index({ email: 1 });\nConsultationSchema.index({ createdAt: -1 });\nConsultationSchema.index({ status: 1 });\n\nexport default mongoose.models.Consultation || mongoose.model('Consultation', ConsultationSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,qBAAqB,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IAC7C,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAA0C;IAC7D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,MAAM;QACN,WAAW;QACX,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;QACN,WAAW;YAAC;YAAI;SAAiD;IACnE;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAsD;IACzE;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;YAAC;YAAQ;YAAU;SAAQ;QACjC,SAAS;IACX;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA8C;IAClE;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAO;YAAa;YAAe;SAAY;QACtD,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;AACF;AAEA,2CAA2C;AAC3C,mBAAmB,GAAG,CAAC,QAAQ,SAAS,IAAI;IAC1C,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;IACzB;AACF;AAEA,8CAA8C;AAC9C,mBAAmB,KAAK,CAAC;IAAE,OAAO;AAAE;AACpC,mBAAmB,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AACzC,mBAAmB,KAAK,CAAC;IAAE,QAAQ;AAAE;uCAEtB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,YAAY,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/app/api/consultation/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Consultation from '@/models/Consultation';\n\n// POST - Create a new consultation request\nexport async function POST(request) {\n  try {\n    // Connect to database\n    await connectDB();\n\n    // Parse request body\n    const body = await request.json();\n    const { name, email, phone, propertyAddress, inquiryType, message } = body;\n\n    // Validate required fields\n    if (!name || !email || !phone || !inquiryType) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Missing required fields: name, email, phone, and inquiry type are required' \n        },\n        { status: 400 }\n      );\n    }\n\n    // Validate email format\n    const emailRegex = /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/;\n    if (!emailRegex.test(email)) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Please provide a valid email address' \n        },\n        { status: 400 }\n      );\n    }\n\n    // Validate inquiry type\n    const validInquiryTypes = ['sell', 'invest', 'other'];\n    if (!validInquiryTypes.includes(inquiryType)) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Invalid inquiry type' \n        },\n        { status: 400 }\n      );\n    }\n\n    // Create new consultation record\n    const consultation = new Consultation({\n      name: name.trim(),\n      email: email.trim().toLowerCase(),\n      phone: phone.trim(),\n      propertyAddress: propertyAddress ? propertyAddress.trim() : '',\n      inquiryType,\n      message: message ? message.trim() : '',\n      status: 'new'\n    });\n\n    // Save to database\n    const savedConsultation = await consultation.save();\n\n    // Return success response\n    return NextResponse.json(\n      {\n        success: true,\n        message: 'Consultation request submitted successfully',\n        data: {\n          id: savedConsultation._id,\n          name: savedConsultation.name,\n          email: savedConsultation.email,\n          inquiryType: savedConsultation.inquiryType,\n          createdAt: savedConsultation.createdAt\n        }\n      },\n      { status: 201 }\n    );\n\n  } catch (error) {\n    console.error('Error creating consultation:', error);\n\n    // Handle mongoose validation errors\n    if (error.name === 'ValidationError') {\n      const validationErrors = Object.values(error.errors).map(err => err.message);\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Validation failed',\n          details: validationErrors\n        },\n        { status: 400 }\n      );\n    }\n\n    // Handle duplicate email error\n    if (error.code === 11000) {\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'A consultation request with this email already exists' \n        },\n        { status: 409 }\n      );\n    }\n\n    // Handle other errors\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Internal server error. Please try again later.' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// GET - Retrieve consultation requests (for admin use)\nexport async function GET(request) {\n  try {\n    // Connect to database\n    await connectDB();\n\n    // Get query parameters\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page')) || 1;\n    const limit = parseInt(searchParams.get('limit')) || 10;\n    const status = searchParams.get('status');\n    const inquiryType = searchParams.get('inquiryType');\n\n    // Build query\n    const query = {};\n    if (status) query.status = status;\n    if (inquiryType) query.inquiryType = inquiryType;\n\n    // Calculate skip value for pagination\n    const skip = (page - 1) * limit;\n\n    // Get consultations with pagination\n    const consultations = await Consultation.find(query)\n      .sort({ createdAt: -1 })\n      .skip(skip)\n      .limit(limit)\n      .select('-__v');\n\n    // Get total count for pagination\n    const total = await Consultation.countDocuments(query);\n\n    return NextResponse.json({\n      success: true,\n      data: consultations,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    });\n\n  } catch (error) {\n    console.error('Error fetching consultations:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Internal server error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// PATCH - Update consultation status (admin only)\nexport async function PATCH(request) {\n  try {\n    // Connect to database\n    await connectDB();\n\n    // Parse request body\n    const body = await request.json();\n    const { id, status } = body;\n\n    // Validate required fields\n    if (!id || !status) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'Consultation ID and status are required'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Validate status value\n    const validStatuses = ['new', 'contacted', 'in-progress', 'completed'];\n    if (!validStatuses.includes(status)) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'Invalid status. Must be one of: ' + validStatuses.join(', ')\n        },\n        { status: 400 }\n      );\n    }\n\n    // Find and update consultation\n    const consultation = await Consultation.findByIdAndUpdate(\n      id,\n      {\n        status: status,\n        updatedAt: new Date()\n      },\n      {\n        new: true,\n        runValidators: true\n      }\n    );\n\n    if (!consultation) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'Consultation not found'\n        },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: 'Consultation status updated successfully',\n      consultation: consultation\n    });\n\n  } catch (error) {\n    console.error('Error updating consultation status:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Internal server error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,sBAAsB;QACtB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,qBAAqB;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;QAEtE,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,oBAAoB;YAAC;YAAQ;YAAU;SAAQ;QACrD,IAAI,CAAC,kBAAkB,QAAQ,CAAC,cAAc;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,eAAe,IAAI,+HAAA,CAAA,UAAY,CAAC;YACpC,MAAM,KAAK,IAAI;YACf,OAAO,MAAM,IAAI,GAAG,WAAW;YAC/B,OAAO,MAAM,IAAI;YACjB,iBAAiB,kBAAkB,gBAAgB,IAAI,KAAK;YAC5D;YACA,SAAS,UAAU,QAAQ,IAAI,KAAK;YACpC,QAAQ;QACV;QAEA,mBAAmB;QACnB,MAAM,oBAAoB,MAAM,aAAa,IAAI;QAEjD,0BAA0B;QAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,IAAI,kBAAkB,GAAG;gBACzB,MAAM,kBAAkB,IAAI;gBAC5B,OAAO,kBAAkB,KAAK;gBAC9B,aAAa,kBAAkB,WAAW;gBAC1C,WAAW,kBAAkB,SAAS;YACxC;QACF,GACA;YAAE,QAAQ;QAAI;IAGlB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,oCAAoC;QACpC,IAAI,MAAM,IAAI,KAAK,mBAAmB;YACpC,MAAM,mBAAmB,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO;YAC3E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;gBACP,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,IAAI,MAAM,IAAI,KAAK,OAAO;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,sBAAsB;QACtB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,uBAAuB;QACvB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,YAAY;QACnD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,aAAa;QACrD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,cAAc,aAAa,GAAG,CAAC;QAErC,cAAc;QACd,MAAM,QAAQ,CAAC;QACf,IAAI,QAAQ,MAAM,MAAM,GAAG;QAC3B,IAAI,aAAa,MAAM,WAAW,GAAG;QAErC,sCAAsC;QACtC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,oCAAoC;QACpC,MAAM,gBAAgB,MAAM,+HAAA,CAAA,UAAY,CAAC,IAAI,CAAC,OAC3C,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,IAAI,CAAC,MACL,KAAK,CAAC,OACN,MAAM,CAAC;QAEV,iCAAiC;QACjC,MAAM,QAAQ,MAAM,+HAAA,CAAA,UAAY,CAAC,cAAc,CAAC;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,MAAM,OAAO;IACjC,IAAI;QACF,sBAAsB;QACtB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,qBAAqB;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG;QAEvB,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,gBAAgB;YAAC;YAAO;YAAa;YAAe;SAAY;QACtE,IAAI,CAAC,cAAc,QAAQ,CAAC,SAAS;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO,qCAAqC,cAAc,IAAI,CAAC;YACjE,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,eAAe,MAAM,+HAAA,CAAA,UAAY,CAAC,iBAAiB,CACvD,IACA;YACE,QAAQ;YACR,WAAW,IAAI;QACjB,GACA;YACE,KAAK;YACL,eAAe;QACjB;QAGF,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,cAAc;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}